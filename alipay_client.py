#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
支付宝API客户端
用于调用支付宝开放平台API获取账单数据
"""

import os
import json
import time
import datetime
from typing import List, Dict, Optional
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

try:
    from alipay import AliPay
    from alipay.utils import AliPayConfig
except ImportError:
    print("请安装支付宝SDK: pip install python-alipay-sdk")
    AliPay = None
    AliPayConfig = None


class AlipayBillClient:
    """支付宝账单客户端"""

    def __init__(self):
        """初始化支付宝客户端"""
        if not AliPay:
            raise ImportError("支付宝SDK未安装，请运行: pip install python-alipay-sdk")
        
        # 从环境变量读取配置
        self.app_id = os.getenv('ALIPAY_APP_ID')
        self.private_key_path = os.getenv('ALIPAY_PRIVATE_KEY_PATH', 'key.pem')
        self.alipay_public_key_path = os.getenv('ALIPAY_PUBLIC_KEY_PATH')
        self.sign_type = os.getenv('ALIPAY_SIGN_TYPE', 'RSA2')
        self.debug = os.getenv('ALIPAY_DEBUG', 'False').lower() == 'true'
        
        if not self.app_id:
            raise ValueError("请在.env文件中设置ALIPAY_APP_ID")
        
        # 读取私钥
        try:
            with open(self.private_key_path, 'r') as f:
                app_private_key = f.read()
        except FileNotFoundError:
            raise FileNotFoundError(f"私钥文件不存在: {self.private_key_path}")
        
        # 读取支付宝公钥（如果提供）
        alipay_public_key = None
        if self.alipay_public_key_path and os.path.exists(self.alipay_public_key_path):
            with open(self.alipay_public_key_path, 'r') as f:
                alipay_public_key = f.read()
        
        # 初始化支付宝客户端
        self.alipay = AliPay(
            appid=self.app_id,
            app_notify_url=None,
            app_private_key_string=app_private_key,
            alipay_public_key_string=alipay_public_key,
            sign_type=self.sign_type,
            debug=self.debug
        )

    def get_bill_download_url(self, bill_type: str = 'trade', bill_date: str = None) -> Optional[str]:
        """
        获取账单下载地址

        Args:
            bill_type: 账单类型 ('trade' 或 'signcustomer')
            bill_date: 账单日期，格式：YYYY-MM-DD

        Returns:
            str: 下载地址，失败返回None
        """
        if not bill_date:
            # 默认获取昨天的账单
            yesterday = datetime.date.today() - datetime.timedelta(days=1)
            bill_date = yesterday.strftime('%Y-%m-%d')

        try:
            # 调用支付宝账单下载API
            result = self.alipay.api_alipay_data_dataservice_bill_downloadurl_query(
                bill_type=bill_type,
                bill_date=bill_date
            )
            
            if result.get('code') == '10000':
                return result.get('bill_download_url')
            else:
                print(f"获取账单下载地址失败: {result.get('msg', '未知错误')}")
                return None
                
        except Exception as e:
            print(f"调用支付宝API异常: {e}")
            return None

    def parse_bill_data(self, bill_content: str) -> List[Dict]:
        """
        解析账单数据

        Args:
            bill_content: 账单文件内容

        Returns:
            List[Dict]: 解析后的账单记录列表
        """
        records = []
        lines = bill_content.strip().split('\n')
        
        # 跳过文件头部信息，找到数据开始行
        data_start_index = -1
        for i, line in enumerate(lines):
            if line.startswith('交易号') or line.startswith('商户订单号'):
                data_start_index = i
                break
        
        if data_start_index == -1:
            print("未找到账单数据开始行")
            return records
        
        # 解析表头
        headers = lines[data_start_index].split(',')
        headers = [h.strip() for h in headers]
        
        # 解析数据行
        for line in lines[data_start_index + 1:]:
            if not line.strip() or line.startswith('#'):
                continue
                
            values = line.split(',')
            if len(values) != len(headers):
                continue
            
            # 创建记录字典
            record = {}
            for i, header in enumerate(headers):
                value = values[i].strip() if i < len(values) else ''
                record[header] = value
            
            # 转换为标准格式
            standardized_record = self._standardize_record(record)
            if standardized_record:
                records.append(standardized_record)
        
        return records

    def _standardize_record(self, record: Dict) -> Optional[Dict]:
        """
        标准化账单记录格式

        Args:
            record: 原始记录

        Returns:
            Dict: 标准化后的记录
        """
        try:
            # 提取关键字段（根据实际账单格式调整）
            alipay_order_no = record.get('交易号', record.get('支付宝交易号', ''))
            if not alipay_order_no:
                return None
            
            # 解析时间
            create_time_str = record.get('交易创建时间', record.get('创建时间', ''))
            if create_time_str:
                try:
                    create_time = datetime.datetime.strptime(create_time_str, '%Y-%m-%d %H:%M:%S')
                    month = create_time.strftime('%Y-%m')
                except ValueError:
                    create_time = datetime.datetime.now()
                    month = create_time.strftime('%Y-%m')
            else:
                create_time = datetime.datetime.now()
                month = create_time.strftime('%Y-%m')
            
            # 解析金额
            amount_str = record.get('金额（元）', record.get('交易金额', '0'))
            try:
                total_amount = float(amount_str.replace('¥', '').replace(',', ''))
            except (ValueError, AttributeError):
                total_amount = 0.0
            
            # 判断交易类型
            trade_type = record.get('交易类型', '')
            if '收入' in trade_type or '转入' in trade_type or total_amount > 0:
                transaction_type = 'income'
            else:
                transaction_type = 'expense'
            
            return {
                'alipay_order_no': alipay_order_no,
                'month': month,
                'gmt_create': create_time,
                'goods_title': record.get('商品名称', record.get('交易对方', '')),
                'total_amount': abs(total_amount),  # 存储绝对值
                'trade_status': record.get('交易状态', 'TRADE_SUCCESS'),
                'trade_type': trade_type,
                'transaction_type': transaction_type,
                'account_id': None,  # 需要根据实际情况设置
                'category_id': None  # 需要根据实际情况设置
            }
            
        except Exception as e:
            print(f"标准化记录失败: {e}, 记录: {record}")
            return None

    def fetch_bills_for_date(self, bill_date: str = None) -> List[Dict]:
        """
        获取指定日期的账单数据

        Args:
            bill_date: 账单日期，格式：YYYY-MM-DD

        Returns:
            List[Dict]: 账单记录列表
        """
        # 获取下载地址
        download_url = self.get_bill_download_url(bill_date=bill_date)
        if not download_url:
            return []
        
        try:
            # 下载账单文件
            import requests
            response = requests.get(download_url, timeout=30)
            response.raise_for_status()
            
            # 解析账单内容
            bill_content = response.text
            return self.parse_bill_data(bill_content)
            
        except Exception as e:
            print(f"下载或解析账单失败: {e}")
            return []


def main():
    """测试函数"""
    try:
        client = AlipayBillClient()
        
        # 获取昨天的账单
        yesterday = datetime.date.today() - datetime.timedelta(days=1)
        bill_date = yesterday.strftime('%Y-%m-%d')
        
        print(f"正在获取 {bill_date} 的账单...")
        bills = client.fetch_bills_for_date(bill_date)
        
        print(f"获取到 {len(bills)} 条账单记录")
        for bill in bills[:5]:  # 只显示前5条
            print(f"订单号: {bill['alipay_order_no']}, 金额: {bill['total_amount']}, 商品: {bill['goods_title']}")
            
    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == '__main__':
    main()
