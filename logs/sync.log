[2025-04-20 23:27:46] 开始执行定时同步检查
[2025-04-20 23:27:46] 定时同步设置: {"enabled":"1","frequency":"hourly","last_sync_time":""}
[2025-04-20 23:27:46] 没有上次同步记录，将执行同步
[2025-04-20 23:27:46] 开始执行数据同步
[2025-04-20 23:27:47] 数据同步成功！新增 3 条记录，更新 0 条记录。
[2025-04-20 23:27:47] 定时同步检查完成
[2025-04-20 23:32:30] 开始执行定时同步检查
[2025-04-20 23:32:30] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-20 23:27:47"}
[2025-04-20 23:32:30] 按分钟模式，间隔设置为1分钟，已经过去4分钟
[2025-04-20 23:32:30] 上次同步时间: 2025-04-20 23:27:47, 频率: minutes, 是否需要同步: 是
[2025-04-20 23:32:30] 开始执行数据同步
[2025-04-20 23:32:31] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-20 23:32:31] 定时同步检查完成
[2025-04-20 23:34:32] 开始执行定时同步检查
[2025-04-20 23:34:32] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-20 23:32:31"}
[2025-04-20 23:34:32] 按分钟模式，间隔设置为1分钟，已经过去2分钟
[2025-04-20 23:34:32] 上次同步时间: 2025-04-20 23:32:31, 频率: minutes, 是否需要同步: 是
[2025-04-20 23:34:32] 开始执行数据同步
[2025-04-20 23:34:32] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-20 23:34:32] 定时同步检查完成
[2025-04-21 00:13:41] 开始执行定时同步检查
[2025-04-21 00:13:41] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-20 23:34:32"}
[2025-04-21 00:13:41] 按分钟模式，间隔设置为1分钟，已经过去39分钟
[2025-04-21 00:13:41] 上次同步时间: 2025-04-20 23:34:32, 频率: minutes, 是否需要同步: 是
[2025-04-21 00:13:41] 开始执行数据同步
[2025-04-21 00:13:41] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 00:13:41] 定时同步检查完成
[2025-04-21 00:19:21] 开始执行定时同步检查
[2025-04-21 00:19:21] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 00:13:41"}
[2025-04-21 00:19:21] 按分钟模式，间隔设置为1分钟，已经过去5分钟
[2025-04-21 00:19:21] 上次同步时间: 2025-04-21 00:13:41, 频率: minutes, 是否需要同步: 是
[2025-04-21 00:19:21] 开始执行数据同步
[2025-04-21 00:19:22] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 00:19:22] 定时同步检查完成
[2025-04-21 00:22:33] 开始执行定时同步检查
[2025-04-21 00:22:33] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 00:19:22"}
[2025-04-21 00:22:33] 按分钟模式，间隔设置为1分钟，已经过去3分钟
[2025-04-21 00:22:33] 上次同步时间: 2025-04-21 00:19:22, 频率: minutes, 是否需要同步: 是
[2025-04-21 00:22:33] 开始执行数据同步
[2025-04-21 00:22:33] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 00:22:33] 定时同步检查完成
[2025-04-21 00:23:54] 开始执行定时同步检查
[2025-04-21 00:23:54] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 00:22:33"}
[2025-04-21 00:23:54] 按分钟模式，间隔设置为1分钟，已经过去1分钟
[2025-04-21 00:23:54] 上次同步时间: 2025-04-21 00:22:33, 频率: minutes, 是否需要同步: 是
[2025-04-21 00:23:54] 开始执行数据同步
[2025-04-21 00:23:55] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 00:23:55] 定时同步检查完成
[2025-04-21 00:25:01] 开始执行定时同步检查
[2025-04-21 00:25:01] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 00:23:55"}
[2025-04-21 00:25:01] 按分钟模式，间隔设置为1分钟，已经过去1分钟
[2025-04-21 00:25:01] 上次同步时间: 2025-04-21 00:23:55, 频率: minutes, 是否需要同步: 是
[2025-04-21 00:25:01] 开始执行数据同步
[2025-04-21 00:25:01] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 00:25:01] 定时同步检查完成
[2025-04-21 09:31:36] 开始执行定时同步检查
[2025-04-21 09:31:36] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 00:25:01"}
[2025-04-21 09:31:36] 按分钟模式，间隔设置为1分钟，已经过去546分钟
[2025-04-21 09:31:36] 上次同步时间: 2025-04-21 00:25:01, 频率: minutes, 是否需要同步: 是
[2025-04-21 09:31:36] 开始执行数据同步
[2025-04-21 09:31:36] 数据同步成功！新增 3 条记录，更新 0 条记录。
[2025-04-21 09:31:36] 定时同步检查完成
[2025-04-21 09:33:41] 开始执行定时同步检查
[2025-04-21 09:33:41] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 09:31:36"}
[2025-04-21 09:33:41] 按分钟模式，间隔设置为1分钟，已经过去2分钟
[2025-04-21 09:33:41] 上次同步时间: 2025-04-21 09:31:36, 频率: minutes, 是否需要同步: 是
[2025-04-21 09:33:41] 开始执行数据同步
[2025-04-21 09:33:41] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 09:33:41] 定时同步检查完成
[2025-04-21 09:38:08] 开始执行定时同步检查
[2025-04-21 09:38:08] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 09:33:41"}
[2025-04-21 09:38:08] 按分钟模式，间隔设置为1分钟，已经过去4分钟
[2025-04-21 09:38:08] 上次同步时间: 2025-04-21 09:33:41, 频率: minutes, 是否需要同步: 是
[2025-04-21 09:38:08] 开始执行数据同步
[2025-04-21 09:38:09] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 09:38:09] 定时同步检查完成
[2025-04-21 09:56:28] 开始执行定时同步检查
[2025-04-21 09:56:28] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 09:38:09"}
[2025-04-21 09:56:28] 按分钟模式，间隔设置为1分钟，已经过去18分钟
[2025-04-21 09:56:28] 上次同步时间: 2025-04-21 09:38:09, 频率: minutes, 是否需要同步: 是
[2025-04-21 09:56:28] 开始执行数据同步
[2025-04-21 09:56:28] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 09:56:28] 定时同步检查完成
[2025-04-21 10:08:56] 开始执行定时同步检查
[2025-04-21 10:08:56] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 09:56:28"}
[2025-04-21 10:08:56] 按分钟模式，间隔设置为1分钟，已经过去12分钟
[2025-04-21 10:08:56] 上次同步时间: 2025-04-21 09:56:28, 频率: minutes, 是否需要同步: 是
[2025-04-21 10:08:56] 开始执行数据同步
[2025-04-21 10:08:56] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 10:08:56] 定时同步检查完成
[2025-04-21 10:10:24] 开始执行定时同步检查
[2025-04-21 10:10:24] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 10:08:56"}
[2025-04-21 10:10:24] 按分钟模式，间隔设置为1分钟，已经过去1分钟
[2025-04-21 10:10:24] 上次同步时间: 2025-04-21 10:08:56, 频率: minutes, 是否需要同步: 是
[2025-04-21 10:10:24] 开始执行数据同步
[2025-04-21 10:10:24] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 10:10:24] 定时同步检查完成
[2025-04-21 10:17:07] 开始执行定时同步检查
[2025-04-21 10:17:07] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 10:10:24"}
[2025-04-21 10:17:07] 按分钟模式，间隔设置为1分钟，已经过去6分钟
[2025-04-21 10:17:07] 上次同步时间: 2025-04-21 10:10:24, 频率: minutes, 是否需要同步: 是
[2025-04-21 10:17:07] 开始执行数据同步
[2025-04-21 10:17:07] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 10:17:07] 定时同步检查完成
[2025-04-21 10:18:43] 开始执行定时同步检查
[2025-04-21 10:18:43] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 10:17:07"}
[2025-04-21 10:18:43] 按分钟模式，间隔设置为1分钟，已经过去1分钟
[2025-04-21 10:18:43] 上次同步时间: 2025-04-21 10:17:07, 频率: minutes, 是否需要同步: 是
[2025-04-21 10:18:43] 开始执行数据同步
[2025-04-21 10:18:43] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 10:18:43] 定时同步检查完成
[2025-04-21 10:20:13] 开始执行定时同步检查
[2025-04-21 10:20:13] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 10:18:43"}
[2025-04-21 10:20:13] 按分钟模式，间隔设置为1分钟，已经过去1分钟
[2025-04-21 10:20:13] 上次同步时间: 2025-04-21 10:18:43, 频率: minutes, 是否需要同步: 是
[2025-04-21 10:20:13] 开始执行数据同步
[2025-04-21 10:20:13] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 10:20:13] 定时同步检查完成
[2025-04-21 10:41:06] 开始执行定时同步检查
[2025-04-21 10:41:06] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 10:20:13"}
[2025-04-21 10:41:06] 按分钟模式，间隔设置为1分钟，已经过去20分钟
[2025-04-21 10:41:06] 上次同步时间: 2025-04-21 10:20:13, 频率: minutes, 是否需要同步: 是
[2025-04-21 10:41:06] 开始执行数据同步
[2025-04-21 10:41:06] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 10:41:06] 定时同步检查完成
[2025-04-21 10:42:12] 开始执行定时同步检查
[2025-04-21 10:42:12] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 10:41:06"}
[2025-04-21 10:42:12] 按分钟模式，间隔设置为1分钟，已经过去1分钟
[2025-04-21 10:42:12] 上次同步时间: 2025-04-21 10:41:06, 频率: minutes, 是否需要同步: 是
[2025-04-21 10:42:12] 开始执行数据同步
[2025-04-21 10:42:13] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 10:42:13] 定时同步检查完成
[2025-04-21 10:51:09] 开始执行定时同步检查
[2025-04-21 10:51:09] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 10:42:13"}
[2025-04-21 10:51:09] 按分钟模式，间隔设置为1分钟，已经过去8分钟
[2025-04-21 10:51:09] 上次同步时间: 2025-04-21 10:42:13, 频率: minutes, 是否需要同步: 是
[2025-04-21 10:51:09] 开始执行数据同步
[2025-04-21 10:51:10] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 10:51:10] 定时同步检查完成
[2025-04-21 10:53:45] 开始执行定时同步检查
[2025-04-21 10:53:45] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 10:51:10"}
[2025-04-21 10:53:45] 按分钟模式，间隔设置为1分钟，已经过去2分钟
[2025-04-21 10:53:45] 上次同步时间: 2025-04-21 10:51:10, 频率: minutes, 是否需要同步: 是
[2025-04-21 10:53:45] 开始执行数据同步
[2025-04-21 10:53:45] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 10:53:45] 定时同步检查完成
[2025-04-21 11:03:16] 开始执行定时同步检查
[2025-04-21 11:03:16] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 10:53:45"}
[2025-04-21 11:03:16] 按分钟模式，间隔设置为1分钟，已经过去9分钟
[2025-04-21 11:03:16] 上次同步时间: 2025-04-21 10:53:45, 频率: minutes, 是否需要同步: 是
[2025-04-21 11:03:16] 开始执行数据同步
[2025-04-21 11:03:17] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 11:03:17] 定时同步检查完成
[2025-04-21 11:09:23] 开始执行定时同步检查
[2025-04-21 11:09:23] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 11:03:17"}
[2025-04-21 11:09:23] 按分钟模式，间隔设置为1分钟，已经过去6分钟
[2025-04-21 11:09:23] 上次同步时间: 2025-04-21 11:03:17, 频率: minutes, 是否需要同步: 是
[2025-04-21 11:09:23] 开始执行数据同步
[2025-04-21 11:09:24] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 11:09:24] 定时同步检查完成
[2025-04-21 11:11:21] 开始执行定时同步检查
[2025-04-21 11:11:21] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 11:09:24"}
[2025-04-21 11:11:21] 按分钟模式，间隔设置为1分钟，已经过去1分钟
[2025-04-21 11:11:21] 上次同步时间: 2025-04-21 11:09:24, 频率: minutes, 是否需要同步: 是
[2025-04-21 11:11:21] 开始执行数据同步
[2025-04-21 11:11:22] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 11:11:22] 定时同步检查完成
[2025-04-21 11:14:10] 开始执行定时同步检查
[2025-04-21 11:14:10] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 11:11:22"}
[2025-04-21 11:14:10] 按分钟模式，间隔设置为1分钟，已经过去2分钟
[2025-04-21 11:14:10] 上次同步时间: 2025-04-21 11:11:22, 频率: minutes, 是否需要同步: 是
[2025-04-21 11:14:10] 开始执行数据同步
[2025-04-21 11:14:11] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 11:14:11] 定时同步检查完成
[2025-04-21 11:22:51] 开始执行定时同步检查
[2025-04-21 11:22:51] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 11:14:11"}
[2025-04-21 11:22:51] 按分钟模式，间隔设置为1分钟，已经过去8分钟
[2025-04-21 11:22:51] 上次同步时间: 2025-04-21 11:14:11, 频率: minutes, 是否需要同步: 是
[2025-04-21 11:22:51] 开始执行数据同步
[2025-04-21 11:22:51] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 11:22:51] 定时同步检查完成
[2025-04-21 11:34:05] 开始执行定时同步检查
[2025-04-21 11:34:05] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 11:22:51"}
[2025-04-21 11:34:05] 按分钟模式，间隔设置为1分钟，已经过去11分钟
[2025-04-21 11:34:05] 上次同步时间: 2025-04-21 11:22:51, 频率: minutes, 是否需要同步: 是
[2025-04-21 11:34:05] 开始执行数据同步
[2025-04-21 11:34:06] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 11:34:06] 定时同步检查完成
[2025-04-21 11:37:50] 开始执行定时同步检查
[2025-04-21 11:37:50] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 11:34:06"}
[2025-04-21 11:37:50] 按分钟模式，间隔设置为1分钟，已经过去3分钟
[2025-04-21 11:37:50] 上次同步时间: 2025-04-21 11:34:06, 频率: minutes, 是否需要同步: 是
[2025-04-21 11:37:50] 开始执行数据同步
[2025-04-21 11:37:51] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 11:37:51] 定时同步检查完成
[2025-04-21 12:00:31] 开始执行定时同步检查
[2025-04-21 12:00:31] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 11:37:51"}
[2025-04-21 12:00:31] 按分钟模式，间隔设置为1分钟，已经过去22分钟
[2025-04-21 12:00:31] 上次同步时间: 2025-04-21 11:37:51, 频率: minutes, 是否需要同步: 是
[2025-04-21 12:00:31] 开始执行数据同步
[2025-04-21 12:00:31] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 12:00:31] 定时同步检查完成
[2025-04-21 13:09:07] 开始执行定时同步检查
[2025-04-21 13:09:07] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 12:00:31"}
[2025-04-21 13:09:07] 按分钟模式，间隔设置为1分钟，已经过去68分钟
[2025-04-21 13:09:07] 上次同步时间: 2025-04-21 12:00:31, 频率: minutes, 是否需要同步: 是
[2025-04-21 13:09:07] 开始执行数据同步
[2025-04-21 13:09:07] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 13:09:07] 定时同步检查完成
[2025-04-21 13:10:22] 开始执行定时同步检查
[2025-04-21 13:10:22] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 13:09:07"}
[2025-04-21 13:10:22] 按分钟模式，间隔设置为1分钟，已经过去1分钟
[2025-04-21 13:10:22] 上次同步时间: 2025-04-21 13:09:07, 频率: minutes, 是否需要同步: 是
[2025-04-21 13:10:22] 开始执行数据同步
[2025-04-21 13:10:23] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 13:10:23] 定时同步检查完成
[2025-04-21 13:11:26] 开始执行定时同步检查
[2025-04-21 13:11:26] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 13:10:23"}
[2025-04-21 13:11:26] 按分钟模式，间隔设置为1分钟，已经过去1分钟
[2025-04-21 13:11:26] 上次同步时间: 2025-04-21 13:10:23, 频率: minutes, 是否需要同步: 是
[2025-04-21 13:11:26] 开始执行数据同步
[2025-04-21 13:11:26] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 13:11:26] 定时同步检查完成
[2025-04-21 13:12:57] 开始执行定时同步检查
[2025-04-21 13:12:57] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 13:11:26"}
[2025-04-21 13:12:57] 按分钟模式，间隔设置为1分钟，已经过去1分钟
[2025-04-21 13:12:57] 上次同步时间: 2025-04-21 13:11:26, 频率: minutes, 是否需要同步: 是
[2025-04-21 13:12:57] 开始执行数据同步
[2025-04-21 13:12:58] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 13:12:58] 定时同步检查完成
[2025-04-21 13:20:12] 开始执行定时同步检查
[2025-04-21 13:20:12] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 13:12:58"}
[2025-04-21 13:20:12] 按分钟模式，间隔设置为1分钟，已经过去7分钟
[2025-04-21 13:20:12] 上次同步时间: 2025-04-21 13:12:58, 频率: minutes, 是否需要同步: 是
[2025-04-21 13:20:12] 开始执行数据同步
[2025-04-21 13:20:12] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 13:20:12] 定时同步检查完成
[2025-04-21 13:21:43] 开始执行定时同步检查
[2025-04-21 13:21:43] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 13:20:12"}
[2025-04-21 13:21:43] 按分钟模式，间隔设置为1分钟，已经过去1分钟
[2025-04-21 13:21:43] 上次同步时间: 2025-04-21 13:20:12, 频率: minutes, 是否需要同步: 是
[2025-04-21 13:21:43] 开始执行数据同步
[2025-04-21 13:21:43] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 13:21:43] 定时同步检查完成
[2025-04-21 13:22:45] 开始执行定时同步检查
[2025-04-21 13:22:45] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 13:21:43"}
[2025-04-21 13:22:45] 按分钟模式，间隔设置为1分钟，已经过去1分钟
[2025-04-21 13:22:45] 上次同步时间: 2025-04-21 13:21:43, 频率: minutes, 是否需要同步: 是
[2025-04-21 13:22:45] 开始执行数据同步
[2025-04-21 13:22:45] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 13:22:45] 定时同步检查完成
[2025-04-21 14:03:47] 开始执行定时同步检查
[2025-04-21 14:03:47] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 13:22:45"}
[2025-04-21 14:03:47] 按分钟模式，间隔设置为1分钟，已经过去41分钟
[2025-04-21 14:03:47] 上次同步时间: 2025-04-21 13:22:45, 频率: minutes, 是否需要同步: 是
[2025-04-21 14:03:47] 开始执行数据同步
[2025-04-21 14:03:47] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 14:03:47] 定时同步检查完成
[2025-04-21 14:06:38] 开始执行定时同步检查
[2025-04-21 14:06:38] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 14:03:47"}
[2025-04-21 14:06:38] 按分钟模式，间隔设置为1分钟，已经过去2分钟
[2025-04-21 14:06:38] 上次同步时间: 2025-04-21 14:03:47, 频率: minutes, 是否需要同步: 是
[2025-04-21 14:06:38] 开始执行数据同步
[2025-04-21 14:06:39] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 14:06:39] 定时同步检查完成
[2025-04-21 15:31:18] 开始执行定时同步检查
[2025-04-21 15:31:18] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 14:06:39"}
[2025-04-21 15:31:18] 按分钟模式，间隔设置为1分钟，已经过去84分钟
[2025-04-21 15:31:18] 上次同步时间: 2025-04-21 14:06:39, 频率: minutes, 是否需要同步: 是
[2025-04-21 15:31:18] 开始执行数据同步
[2025-04-21 15:31:19] 数据同步成功！新增 1 条记录，更新 0 条记录。
[2025-04-21 15:31:19] 定时同步检查完成
[2025-04-21 15:33:56] 开始执行定时同步检查
[2025-04-21 15:33:56] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 15:31:19"}
[2025-04-21 15:33:56] 按分钟模式，间隔设置为1分钟，已经过去2分钟
[2025-04-21 15:33:56] 上次同步时间: 2025-04-21 15:31:19, 频率: minutes, 是否需要同步: 是
[2025-04-21 15:33:56] 开始执行数据同步
[2025-04-21 15:33:57] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 15:33:57] 定时同步检查完成
[2025-04-21 15:35:20] 开始执行定时同步检查
[2025-04-21 15:35:20] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 15:33:57"}
[2025-04-21 15:35:20] 按分钟模式，间隔设置为1分钟，已经过去1分钟
[2025-04-21 15:35:20] 上次同步时间: 2025-04-21 15:33:57, 频率: minutes, 是否需要同步: 是
[2025-04-21 15:35:20] 开始执行数据同步
[2025-04-21 15:35:20] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 15:35:20] 定时同步检查完成
[2025-04-21 15:54:28] 开始执行定时同步检查
[2025-04-21 15:54:28] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 15:35:20"}
[2025-04-21 15:54:28] 按分钟模式，间隔设置为1分钟，已经过去19分钟
[2025-04-21 15:54:28] 上次同步时间: 2025-04-21 15:35:20, 频率: minutes, 是否需要同步: 是
[2025-04-21 15:54:28] 开始执行数据同步
[2025-04-21 15:54:29] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 15:54:29] 定时同步检查完成
[2025-04-21 15:59:21] 开始执行定时同步检查
[2025-04-21 15:59:21] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 15:54:29"}
[2025-04-21 15:59:21] 按分钟模式，间隔设置为1分钟，已经过去4分钟
[2025-04-21 15:59:21] 上次同步时间: 2025-04-21 15:54:29, 频率: minutes, 是否需要同步: 是
[2025-04-21 15:59:21] 开始执行数据同步
[2025-04-21 15:59:21] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 15:59:21] 定时同步检查完成
[2025-04-21 16:00:21] 开始执行定时同步检查
[2025-04-21 16:00:21] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 15:59:21"}
[2025-04-21 16:00:21] 按分钟模式，间隔设置为1分钟，已经过去1分钟
[2025-04-21 16:00:21] 上次同步时间: 2025-04-21 15:59:21, 频率: minutes, 是否需要同步: 是
[2025-04-21 16:00:21] 开始执行数据同步
[2025-04-21 16:00:21] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 16:00:21] 定时同步检查完成
[2025-04-21 16:10:19] 开始执行定时同步检查
[2025-04-21 16:10:19] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 16:00:21"}
[2025-04-21 16:10:19] 按分钟模式，间隔设置为1分钟，已经过去9分钟
[2025-04-21 16:10:19] 上次同步时间: 2025-04-21 16:00:21, 频率: minutes, 是否需要同步: 是
[2025-04-21 16:10:19] 开始执行数据同步
[2025-04-21 16:10:20] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 16:10:20] 定时同步检查完成
[2025-04-21 16:18:31] 开始执行定时同步检查
[2025-04-21 16:18:31] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 16:10:20"}
[2025-04-21 16:18:31] 按分钟模式，间隔设置为1分钟，已经过去8分钟
[2025-04-21 16:18:31] 上次同步时间: 2025-04-21 16:10:20, 频率: minutes, 是否需要同步: 是
[2025-04-21 16:18:31] 开始执行数据同步
[2025-04-21 16:18:32] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 16:18:32] 定时同步检查完成
[2025-04-21 16:19:41] 开始执行定时同步检查
[2025-04-21 16:19:41] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 16:18:32"}
[2025-04-21 16:19:41] 按分钟模式，间隔设置为1分钟，已经过去1分钟
[2025-04-21 16:19:41] 上次同步时间: 2025-04-21 16:18:32, 频率: minutes, 是否需要同步: 是
[2025-04-21 16:19:41] 开始执行数据同步
[2025-04-21 16:19:41] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 16:19:41] 定时同步检查完成
[2025-04-21 16:23:10] 开始执行定时同步检查
[2025-04-21 16:23:10] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 16:19:41"}
[2025-04-21 16:23:10] 按分钟模式，间隔设置为1分钟，已经过去3分钟
[2025-04-21 16:23:10] 上次同步时间: 2025-04-21 16:19:41, 频率: minutes, 是否需要同步: 是
[2025-04-21 16:23:10] 开始执行数据同步
[2025-04-21 16:23:10] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 16:23:10] 定时同步检查完成
[2025-04-21 16:24:16] 开始执行定时同步检查
[2025-04-21 16:24:16] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 16:23:10"}
[2025-04-21 16:24:16] 按分钟模式，间隔设置为1分钟，已经过去1分钟
[2025-04-21 16:24:16] 上次同步时间: 2025-04-21 16:23:10, 频率: minutes, 是否需要同步: 是
[2025-04-21 16:24:16] 开始执行数据同步
[2025-04-21 16:24:17] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 16:24:17] 定时同步检查完成
[2025-04-21 16:25:21] 开始执行定时同步检查
[2025-04-21 16:25:21] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-04-21 16:24:17"}
[2025-04-21 16:25:21] 按分钟模式，间隔设置为1分钟，已经过去1分钟
[2025-04-21 16:25:21] 上次同步时间: 2025-04-21 16:24:17, 频率: minutes, 是否需要同步: 是
[2025-04-21 16:25:21] 开始执行数据同步
[2025-04-21 16:25:21] 数据同步成功！新增 0 条记录，更新 0 条记录。
[2025-04-21 16:25:21] 定时同步检查完成
[2025-05-30 15:26:27] 开始执行定时同步检查
[2025-05-30 15:26:27] 定时同步设置: {"enabled":"1","frequency":"minutes","interval":"1","last_sync_time":"2025-05-30 00:31:31"}
[2025-05-30 15:26:27] 按分钟模式，间隔设置为1分钟，已经过去894分钟
[2025-05-30 15:26:27] 上次同步时间: 2025-05-30 00:31:31, 频率: minutes, 是否需要同步: 是
[2025-05-30 15:26:27] 开始执行数据同步
[2025-05-30 15:26:28] 数据同步成功！新增 2 条记录，更新 0 条记录。
[2025-05-30 15:26:28] 定时同步检查完成
