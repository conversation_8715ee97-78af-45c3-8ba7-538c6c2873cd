#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
系统测试脚本
测试数据库连接和基本功能，不依赖支付宝API
"""

import sys
import json
import datetime
from db_connector import DatabaseConnector

def test_database_connection():
    """测试数据库连接"""
    print("=== 测试数据库连接 ===")
    try:
        db = DatabaseConnector()
        print("✓ 数据库连接成功")
        
        # 测试获取记录总数
        count = db.get_transaction_count()
        print(f"✓ 数据库中共有 {count} 条记录")
        
        # 测试获取前5条记录
        records = db.get_all_transactions(limit=5)
        print(f"✓ 成功获取 {len(records)} 条记录")
        
        if records:
            print("前3条记录示例:")
            for i, record in enumerate(records[:3]):
                print(f"  {i+1}. 订单号: {record['alipay_order_no'][:20]}...")
                print(f"     商品: {record['goods_title'][:30]}...")
                print(f"     金额: ¥{record['total_amount']}")
                print(f"     时间: {record['gmt_create']}")
                print()
        
        db.close()
        print("✓ 数据库连接测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 数据库连接测试失败: {e}")
        return False

def test_insert_sample_record():
    """测试插入示例记录"""
    print("\n=== 测试插入示例记录 ===")
    try:
        db = DatabaseConnector()
        
        # 创建测试记录
        test_record = {
            'alipay_order_no': f'TEST_{datetime.datetime.now().strftime("%Y%m%d%H%M%S")}',
            'month': datetime.datetime.now().strftime('%Y-%m'),
            'gmt_create': datetime.datetime.now(),
            'goods_title': '系统测试记录',
            'total_amount': 0.01,
            'trade_status': 'TRADE_SUCCESS',
            'trade_type': '测试交易',
            'transaction_type': 'expense',
            'account_id': 1,
            'category_id': 12
        }
        
        # 尝试插入记录
        result = db.insert_bill_record(test_record)
        if result:
            print(f"✓ 成功插入测试记录: {test_record['alipay_order_no']}")
        else:
            print("✗ 插入测试记录失败（可能已存在）")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"✗ 插入测试记录失败: {e}")
        return False

def test_api_simulation():
    """模拟API调用测试"""
    print("\n=== 模拟API调用测试 ===")
    try:
        # 模拟账单获取结果
        mock_result = {
            'success': True,
            'message': '模拟账单获取完成',
            'data': {
                'date': datetime.date.today().strftime('%Y-%m-%d'),
                'total_fetched': 5,
                'success_stored': 3,
                'duplicates': 2,
                'errors': 0
            }
        }
        
        print("✓ 模拟账单获取成功")
        print(f"  获取日期: {mock_result['data']['date']}")
        print(f"  获取记录: {mock_result['data']['total_fetched']} 条")
        print(f"  成功存储: {mock_result['data']['success_stored']} 条")
        print(f"  重复记录: {mock_result['data']['duplicates']} 条")
        
        return mock_result
        
    except Exception as e:
        print(f"✗ 模拟API调用失败: {e}")
        return None

def main():
    """主测试函数"""
    print("支付宝账单管理系统 - 系统测试")
    print("=" * 50)
    
    # 测试数据库连接
    db_test = test_database_connection()
    
    # 测试插入记录
    insert_test = test_insert_sample_record()
    
    # 模拟API调用
    api_test = test_api_simulation()
    
    print("\n=== 测试总结 ===")
    print(f"数据库连接测试: {'✓ 通过' if db_test else '✗ 失败'}")
    print(f"记录插入测试: {'✓ 通过' if insert_test else '✗ 失败'}")
    print(f"API模拟测试: {'✓ 通过' if api_test else '✗ 失败'}")
    
    if db_test and insert_test and api_test:
        print("\n🎉 所有测试通过！系统基本功能正常。")
        print("\n📝 下一步:")
        print("1. 配置支付宝API密钥（.env文件）")
        print("2. 访问 http://localhost:8000 查看Web界面")
        print("3. 点击'获取最新账单'按钮测试完整功能")
        
        # 输出测试结果供PHP调用
        if api_test:
            return api_test
    else:
        print("\n❌ 部分测试失败，请检查配置。")
        return {
            'success': False,
            'message': '系统测试失败',
            'data': {}
        }

if __name__ == '__main__':
    result = main()
    
    # 如果有命令行参数要求输出JSON
    if len(sys.argv) > 1 and sys.argv[1] == '--json':
        print("\n" + "="*50)
        print("JSON输出:")
        print(json.dumps(result, ensure_ascii=False, indent=2, default=str))
