#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
系统测试脚本
测试数据库连接和基本功能，不依赖支付宝API
"""

import sys
import json
import datetime
from db_connector import DatabaseConnector

def test_database_connection():
    """测试数据库连接"""
    print("=== 测试数据库连接 ===")
    try:
        db = DatabaseConnector()
        print("✓ 数据库连接成功")
        
        # 测试获取记录总数
        count = db.get_transaction_count()
        print(f"✓ 数据库中共有 {count} 条记录")
        
        # 测试获取前5条记录
        records = db.get_all_transactions(limit=5)
        print(f"✓ 成功获取 {len(records)} 条记录")
        
        if records:
            print("前3条记录示例:")
            for i, record in enumerate(records[:3]):
                print(f"  {i+1}. 订单号: {record['alipay_order_no'][:20]}...")
                print(f"     商品: {record['goods_title'][:30]}...")
                print(f"     金额: ¥{record['total_amount']}")
                print(f"     时间: {record['gmt_create']}")
                print()
        
        db.close()
        print("✓ 数据库连接测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 数据库连接测试失败: {e}")
        return False

def test_insert_sample_record():
    """测试插入示例记录"""
    print("=== 测试插入示例记录 ===")
    try:
        db = DatabaseConnector()
        
        # 创建测试记录
        test_record = {
            'alipay_order_no': f'TEST_{datetime.datetime.now().strftime("%Y%m%d%H%M%S")}',
            'month': datetime.datetime.now().strftime('%Y-%m'),
            'gmt_create': datetime.datetime.now(),
            'goods_title': '系统测试记录',
            'total_amount': 0.01,
            'trade_status': 'TRADE_SUCCESS',
            'trade_type': '测试交易',
            'transaction_type': 'expense',
            'account_id': None,
            'category_id': None
        }
        
        # 尝试插入记录
        result = db.insert_bill_record(test_record)
        if result:
            print("✓ 成功插入测试记录")
            
            # 验证记录是否存在
            records = db.get_all_transactions(limit=1)
            if records and records[0]['alipay_order_no'] == test_record['alipay_order_no']:
                print("✓ 记录验证成功")
            else:
                print("✗ 记录验证失败")
                return False
        else:
            print("✗ 插入测试记录失败")
            return False
        
        db.close()
        print("✓ 插入测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 插入测试失败: {e}")
        return False

def test_api_simulation():
    """模拟API调用测试"""
    print("=== 模拟API调用测试 ===")
    try:
        # 模拟成功的API响应
        mock_bills = [
            {
                'alipay_order_no': f'MOCK_{datetime.datetime.now().strftime("%Y%m%d%H%M%S")}_001',
                'month': datetime.datetime.now().strftime('%Y-%m'),
                'gmt_create': datetime.datetime.now(),
                'goods_title': '模拟交易记录1',
                'total_amount': 10.50,
                'trade_status': 'TRADE_SUCCESS',
                'trade_type': '即时到账交易',
                'transaction_type': 'expense',
                'account_id': None,
                'category_id': None
            },
            {
                'alipay_order_no': f'MOCK_{datetime.datetime.now().strftime("%Y%m%d%H%M%S")}_002',
                'month': datetime.datetime.now().strftime('%Y-%m'),
                'gmt_create': datetime.datetime.now(),
                'goods_title': '模拟交易记录2',
                'total_amount': 25.00,
                'trade_status': 'TRADE_SUCCESS',
                'trade_type': '即时到账交易',
                'transaction_type': 'income',
                'account_id': None,
                'category_id': None
            }
        ]
        
        db = DatabaseConnector()
        success_count = 0
        
        for bill in mock_bills:
            try:
                result = db.insert_bill_record(bill)
                if result:
                    success_count += 1
            except Exception as e:
                print(f"插入模拟记录失败: {e}")
        
        print(f"✓ 成功插入 {success_count}/{len(mock_bills)} 条模拟记录")
        
        db.close()
        
        # 返回模拟的API响应结果
        api_result = {
            'success': True,
            'message': f'模拟获取账单成功',
            'data': {
                'date': datetime.date.today().strftime('%Y-%m-%d'),
                'total_count': len(mock_bills),
                'success_count': success_count,
                'duplicate_count': len(mock_bills) - success_count,
                'error_count': 0
            }
        }
        
        print("✓ API模拟测试完成")
        return api_result
        
    except Exception as e:
        print(f"✗ API模拟测试失败: {e}")
        return {
            'success': False,
            'message': f'模拟测试失败: {e}',
            'data': {}
        }

def main():
    """主测试函数"""
    print("支付宝账单管理系统 - 系统测试")
    print("=" * 50)
    
    # 测试数据库连接
    db_test = test_database_connection()
    
    # 测试插入记录
    insert_test = test_insert_sample_record()
    
    # 模拟API调用
    api_test = test_api_simulation()
    
    print("\n=== 测试总结 ===")
    print(f"数据库连接测试: {'✓ 通过' if db_test else '✗ 失败'}")
    print(f"记录插入测试: {'✓ 通过' if insert_test else '✗ 失败'}")
    print(f"API模拟测试: {'✓ 通过' if api_test else '✗ 失败'}")
    
    if db_test and insert_test and api_test:
        print("\n🎉 所有测试通过！系统基本功能正常。")
        print("\n📝 下一步:")
        print("1. 配置支付宝API密钥（.env文件）")
        print("2. 运行 python bill_fetcher.py 获取真实账单")
        print("3. 使用 python bill_fetcher.py --date 2024-01-01 获取指定日期账单")
        
        # 输出测试结果供PHP调用
        if api_test:
            return api_test
    else:
        print("\n❌ 部分测试失败，请检查配置。")
        return {
            'success': False,
            'message': '系统测试失败',
            'data': {}
        }

if __name__ == '__main__':
    try:
        result = main()
        if result:
            # 如果有结果，输出JSON格式供其他程序调用
            print("\n" + "=" * 50)
            print("JSON输出:")
            print(json.dumps(result, ensure_ascii=False, indent=2, default=str))
    except Exception as e:
        print(f"测试程序执行失败: {e}")
        sys.exit(1)
