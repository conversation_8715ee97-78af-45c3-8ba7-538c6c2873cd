<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付宝账单管理系统</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>支付宝账单管理系统</h1>
            <div class="header-actions">
                <button id="fetchBillBtn" class="btn btn-primary">获取最新账单</button>
                <button id="refreshBtn" class="btn btn-secondary">刷新数据</button>
            </div>
        </header>

        <div class="stats-section">
            <div class="stat-card">
                <h3>总记录数</h3>
                <span id="totalCount" class="stat-number">-</span>
            </div>
            <div class="stat-card">
                <h3>本月支出</h3>
                <span id="monthExpense" class="stat-number">-</span>
            </div>
            <div class="stat-card">
                <h3>本月收入</h3>
                <span id="monthIncome" class="stat-number">-</span>
            </div>
        </div>

        <div class="controls-section">
            <div class="search-controls">
                <input type="text" id="searchInput" placeholder="搜索商品名称或订单号...">
                <select id="typeFilter">
                    <option value="">全部类型</option>
                    <option value="expense">支出</option>
                    <option value="income">收入</option>
                </select>
                <input type="month" id="monthFilter">
                <button id="searchBtn" class="btn btn-secondary">搜索</button>
                <button id="clearBtn" class="btn btn-light">清除</button>
            </div>
        </div>

        <div class="table-section">
            <div class="table-header">
                <h2>账单记录</h2>
                <div class="pagination-info">
                    <span id="paginationInfo">第 1 页，共 0 条记录</span>
                </div>
            </div>
            
            <div class="table-container">
                <table id="billTable">
                    <thead>
                        <tr>
                            <th>订单号</th>
                            <th>创建时间</th>
                            <th>商品名称</th>
                            <th>金额</th>
                            <th>类型</th>
                            <th>状态</th>
                            <th>分类</th>
                            <th>账户</th>
                        </tr>
                    </thead>
                    <tbody id="billTableBody">
                        <tr>
                            <td colspan="8" class="loading">加载中...</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="pagination">
                <button id="prevBtn" class="btn btn-light">上一页</button>
                <span id="pageNumbers"></span>
                <button id="nextBtn" class="btn btn-light">下一页</button>
            </div>
        </div>

        <div id="loadingModal" class="modal">
            <div class="modal-content">
                <div class="loading-spinner"></div>
                <p id="loadingText">正在获取账单数据...</p>
            </div>
        </div>

        <div id="messageModal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h3 id="messageTitle">提示</h3>
                <p id="messageText"></p>
                <button id="messageOkBtn" class="btn btn-primary">确定</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
