# 支付宝账单获取工具

一个基于Python的支付宝账单获取工具，通过支付宝开放平台API自动获取账单数据并存储到数据库。

## 功能特性

- 🔄 **自动账单获取**: 通过支付宝开放平台API自动获取账单数据
- 💾 **数据存储**: 将账单数据存储到MySQL数据库
- 🔍 **重复检测**: 自动检测并避免重复数据插入
- 📊 **数据解析**: 解析支付宝账单文件格式
- 🛠️ **测试工具**: 提供完整的测试脚本

## 技术栈

- **后端**: Python 3.8+
- **数据库**: MySQL 8.0+
- **API**: 支付宝开放平台API

## 系统要求

- Python 3.8 或更高版本
- MySQL 8.0 或更高版本
- 支付宝开放平台开发者账号

## 安装部署

### 1. 克隆项目

```bash
git clone <repository-url>
cd alipay-bill-fetcher
```

### 2. 安装Python依赖

```bash
pip install -r requirements.txt
```

### 3. 数据库配置

1. 创建MySQL数据库
2. 创建zfb表结构
3. 配置数据库连接信息

### 4. 环境配置

复制环境配置文件并填写相关信息：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置以下信息：

```env
# 数据库配置
DB_HOST=127.0.0.1
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=zfb_data

# 支付宝API配置
ALIPAY_APP_ID=your_app_id
ALIPAY_PRIVATE_KEY_PATH=key.pem
ALIPAY_PUBLIC_KEY_PATH=alipay_public_key.pem
ALIPAY_SIGN_TYPE=RSA2
ALIPAY_DEBUG=False
```

### 5. 支付宝API配置

1. 在支付宝开放平台创建应用
2. 获取应用ID和密钥对
3. 将私钥保存为 `key.pem` 文件
4. 将支付宝公钥保存为 `alipay_public_key.pem` 文件

## 使用说明

### 账单获取

```bash
# 获取昨天的账单
python bill_fetcher.py

# 获取指定日期的账单
python bill_fetcher.py --date 2024-01-01
```

### 测试系统

```bash
# 运行系统测试
python test_system.py
```

### 直接使用API客户端

```python
from alipay_client import AlipayBillClient

# 创建客户端
client = AlipayBillClient()

# 获取账单数据
bills = client.fetch_bills_for_date('2024-01-01')
print(f"获取到 {len(bills)} 条账单记录")
```

## 文件说明

- `alipay_client.py` - 支付宝API客户端，负责调用支付宝API获取账单
- `bill_fetcher.py` - 账单获取主程序，整合API调用和数据存储
- `db_connector.py` - 数据库连接器，负责数据库操作
- `test_system.py` - 系统测试脚本，验证各组件功能
- `requirements.txt` - Python依赖包列表
- `.env.example` - 环境变量配置示例
- `key.pem` - 支付宝私钥文件
- `config/database.php` - 数据库配置文件

## 数据库表结构

```sql
CREATE TABLE zfb (
    id INT AUTO_INCREMENT PRIMARY KEY,
    alipay_order_no VARCHAR(64) NOT NULL,
    month VARCHAR(7) NOT NULL,
    gmt_create DATETIME NOT NULL,
    goods_title TEXT,
    total_amount DECIMAL(10,2) NOT NULL,
    trade_status VARCHAR(32),
    trade_type VARCHAR(32),
    transaction_type VARCHAR(16),
    account_id INT,
    category_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_order_month (alipay_order_no, month)
);
```

## 常见问题

### 1. 支付宝API调用失败

- 检查应用ID和密钥配置是否正确
- 确认应用已获得相应API权限
- 检查网络连接和防火墙设置

### 2. 数据库连接失败

- 检查数据库服务是否启动
- 确认数据库连接信息是否正确
- 检查数据库用户权限

### 3. 账单数据重复

系统会自动检测重复记录，相同订单号和月份的记录不会重复插入。

## 许可证

本项目采用 MIT 许可证。
