/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

header h1 {
    color: #1890ff;
    font-size: 24px;
}

.header-actions {
    display: flex;
    gap: 10px;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background-color: #1890ff;
    color: white;
}

.btn-primary:hover {
    background-color: #40a9ff;
}

.btn-secondary {
    background-color: #f0f0f0;
    color: #333;
}

.btn-secondary:hover {
    background-color: #d9d9d9;
}

.btn-light {
    background-color: white;
    color: #333;
    border: 1px solid #d9d9d9;
}

.btn-light:hover {
    background-color: #f5f5f5;
}

/* 统计卡片 */
.stats-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.stat-card h3 {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #1890ff;
}

/* 控制区域 */
.controls-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.search-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    align-items: center;
}

.search-controls input,
.search-controls select {
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
}

.search-controls input[type="text"] {
    flex: 1;
    min-width: 200px;
}

/* 表格区域 */
.table-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.table-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-header h2 {
    font-size: 18px;
    color: #333;
}

.pagination-info {
    color: #666;
    font-size: 14px;
}

.table-container {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

th {
    background-color: #fafafa;
    font-weight: 600;
    color: #333;
}

tr:hover {
    background-color: #f5f5f5;
}

.loading {
    text-align: center;
    color: #666;
    font-style: italic;
}

/* 分页 */
.pagination {
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

#pageNumbers {
    display: flex;
    gap: 5px;
}

.page-number {
    padding: 6px 12px;
    border: 1px solid #d9d9d9;
    background: white;
    cursor: pointer;
    border-radius: 4px;
    font-size: 14px;
}

.page-number.active {
    background-color: #1890ff;
    color: white;
    border-color: #1890ff;
}

.page-number:hover:not(.active) {
    background-color: #f5f5f5;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 15% auto;
    padding: 20px;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    position: relative;
}

.close {
    position: absolute;
    right: 15px;
    top: 15px;
    font-size: 24px;
    cursor: pointer;
    color: #999;
}

.close:hover {
    color: #333;
}

/* 加载动画 */
.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #1890ff;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .search-controls {
        flex-direction: column;
    }
    
    .search-controls input,
    .search-controls select {
        width: 100%;
    }
    
    .stats-section {
        grid-template-columns: 1fr;
    }
    
    .table-container {
        font-size: 14px;
    }
    
    th, td {
        padding: 8px;
    }
}

/* 交易类型标签 */
.transaction-type {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.transaction-type.expense {
    background-color: #fff2e8;
    color: #fa8c16;
}

.transaction-type.income {
    background-color: #f6ffed;
    color: #52c41a;
}

/* 金额样式 */
.amount {
    font-weight: 600;
}

.amount.expense {
    color: #fa8c16;
}

.amount.income {
    color: #52c41a;
}
