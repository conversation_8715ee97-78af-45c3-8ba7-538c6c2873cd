// 全局变量
let currentPage = 1;
let totalPages = 1;
let isLoading = false;

// DOM元素
const elements = {
    fetchBillBtn: document.getElementById('fetchBillBtn'),
    refreshBtn: document.getElementById('refreshBtn'),
    searchBtn: document.getElementById('searchBtn'),
    clearBtn: document.getElementById('clearBtn'),
    prevBtn: document.getElementById('prevBtn'),
    nextBtn: document.getElementById('nextBtn'),
    
    searchInput: document.getElementById('searchInput'),
    typeFilter: document.getElementById('typeFilter'),
    monthFilter: document.getElementById('monthFilter'),
    
    totalCount: document.getElementById('totalCount'),
    monthExpense: document.getElementById('monthExpense'),
    monthIncome: document.getElementById('monthIncome'),
    
    billTableBody: document.getElementById('billTableBody'),
    paginationInfo: document.getElementById('paginationInfo'),
    pageNumbers: document.getElementById('pageNumbers'),
    
    loadingModal: document.getElementById('loadingModal'),
    messageModal: document.getElementById('messageModal'),
    loadingText: document.getElementById('loadingText'),
    messageTitle: document.getElementById('messageTitle'),
    messageText: document.getElementById('messageText'),
    messageOkBtn: document.getElementById('messageOkBtn')
};

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    loadStats();
    loadBills();
});

// 事件监听器
function initializeEventListeners() {
    elements.fetchBillBtn.addEventListener('click', fetchBills);
    elements.refreshBtn.addEventListener('click', refreshData);
    elements.searchBtn.addEventListener('click', searchBills);
    elements.clearBtn.addEventListener('click', clearSearch);
    elements.prevBtn.addEventListener('click', () => changePage(currentPage - 1));
    elements.nextBtn.addEventListener('click', () => changePage(currentPage + 1));
    
    // 回车搜索
    elements.searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchBills();
        }
    });
    
    // 模态框关闭
    document.querySelector('.close').addEventListener('click', closeMessageModal);
    elements.messageOkBtn.addEventListener('click', closeMessageModal);
    
    // 点击模态框外部关闭
    elements.messageModal.addEventListener('click', function(e) {
        if (e.target === elements.messageModal) {
            closeMessageModal();
        }
    });
}

// 加载统计数据
async function loadStats() {
    try {
        const response = await axios.get('api.php?action=get_stats');
        if (response.data.success) {
            const stats = response.data.data;
            elements.totalCount.textContent = stats.total_count.toLocaleString();
            elements.monthExpense.textContent = '¥' + stats.month_expense.toLocaleString();
            elements.monthIncome.textContent = '¥' + stats.month_income.toLocaleString();
        }
    } catch (error) {
        console.error('加载统计数据失败:', error);
    }
}

// 加载账单数据
async function loadBills(page = 1) {
    if (isLoading) return;
    
    isLoading = true;
    currentPage = page;
    
    try {
        elements.billTableBody.innerHTML = '<tr><td colspan="8" class="loading">加载中...</td></tr>';
        
        const response = await axios.get(`api.php?action=get_bills&page=${page}&limit=20`);
        
        if (response.data.success) {
            const { data: bills, pagination } = response.data;
            totalPages = pagination.pages;
            
            renderBillTable(bills);
            updatePagination(pagination);
        } else {
            throw new Error(response.data.error || '加载失败');
        }
    } catch (error) {
        console.error('加载账单数据失败:', error);
        elements.billTableBody.innerHTML = '<tr><td colspan="8" class="loading">加载失败</td></tr>';
        showMessage('错误', '加载账单数据失败: ' + error.message);
    } finally {
        isLoading = false;
    }
}

// 渲染账单表格
function renderBillTable(bills) {
    if (bills.length === 0) {
        elements.billTableBody.innerHTML = '<tr><td colspan="8" class="loading">暂无数据</td></tr>';
        return;
    }
    
    const rows = bills.map(bill => {
        const typeClass = bill.transaction_type === 'expense' ? 'expense' : 'income';
        const typeText = bill.transaction_type === 'expense' ? '支出' : '收入';
        
        return `
            <tr>
                <td title="${bill.alipay_order_no}">${bill.alipay_order_no.substring(0, 20)}...</td>
                <td>${bill.gmt_create}</td>
                <td title="${bill.goods_title}">${bill.goods_title.substring(0, 30)}${bill.goods_title.length > 30 ? '...' : ''}</td>
                <td class="amount ${typeClass}">¥${bill.total_amount.toLocaleString()}</td>
                <td><span class="transaction-type ${typeClass}">${typeText}</span></td>
                <td>${bill.trade_status || '-'}</td>
                <td>${bill.category_name || '-'}</td>
                <td>${bill.account_name || '-'}</td>
            </tr>
        `;
    }).join('');
    
    elements.billTableBody.innerHTML = rows;
}

// 更新分页
function updatePagination(pagination) {
    const { page, total, pages } = pagination;
    
    elements.paginationInfo.textContent = `第 ${page} 页，共 ${total.toLocaleString()} 条记录`;
    
    // 更新按钮状态
    elements.prevBtn.disabled = page <= 1;
    elements.nextBtn.disabled = page >= pages;
    
    // 生成页码
    generatePageNumbers(page, pages);
}

// 生成页码
function generatePageNumbers(current, total) {
    let pages = [];
    
    if (total <= 7) {
        for (let i = 1; i <= total; i++) {
            pages.push(i);
        }
    } else {
        if (current <= 4) {
            pages = [1, 2, 3, 4, 5, '...', total];
        } else if (current >= total - 3) {
            pages = [1, '...', total - 4, total - 3, total - 2, total - 1, total];
        } else {
            pages = [1, '...', current - 1, current, current + 1, '...', total];
        }
    }
    
    const pageHtml = pages.map(page => {
        if (page === '...') {
            return '<span class="page-number">...</span>';
        } else {
            const activeClass = page === current ? 'active' : '';
            return `<span class="page-number ${activeClass}" onclick="changePage(${page})">${page}</span>`;
        }
    }).join('');
    
    elements.pageNumbers.innerHTML = pageHtml;
}

// 切换页面
function changePage(page) {
    if (page < 1 || page > totalPages || page === currentPage || isLoading) {
        return;
    }
    
    if (isSearching()) {
        searchBills(page);
    } else {
        loadBills(page);
    }
}

// 检查是否在搜索状态
function isSearching() {
    return elements.searchInput.value.trim() || 
           elements.typeFilter.value || 
           elements.monthFilter.value;
}

// 搜索账单
async function searchBills(page = 1) {
    if (isLoading) return;
    
    isLoading = true;
    currentPage = page;
    
    const params = new URLSearchParams({
        action: 'search_bills',
        page: page,
        limit: 20
    });
    
    const keyword = elements.searchInput.value.trim();
    const type = elements.typeFilter.value;
    const month = elements.monthFilter.value;
    
    if (keyword) params.append('keyword', keyword);
    if (type) params.append('type', type);
    if (month) params.append('month', month);
    
    try {
        elements.billTableBody.innerHTML = '<tr><td colspan="8" class="loading">搜索中...</td></tr>';
        
        const response = await axios.get(`api.php?${params.toString()}`);
        
        if (response.data.success) {
            const { data: bills, pagination } = response.data;
            totalPages = pagination.pages;
            
            renderBillTable(bills);
            updatePagination(pagination);
        } else {
            throw new Error(response.data.error || '搜索失败');
        }
    } catch (error) {
        console.error('搜索失败:', error);
        elements.billTableBody.innerHTML = '<tr><td colspan="8" class="loading">搜索失败</td></tr>';
        showMessage('错误', '搜索失败: ' + error.message);
    } finally {
        isLoading = false;
    }
}

// 清除搜索
function clearSearch() {
    elements.searchInput.value = '';
    elements.typeFilter.value = '';
    elements.monthFilter.value = '';
    loadBills(1);
}

// 获取账单
async function fetchBills() {
    if (isLoading) return;
    
    showLoadingModal('正在获取最新账单数据...');
    
    try {
        const formData = new FormData();
        formData.append('action', 'fetch_bills');
        
        const response = await axios.post('api.php', formData, {
            timeout: 120000 // 2分钟超时
        });
        
        hideLoadingModal();
        
        if (response.data.success) {
            const data = response.data.data;
            showMessage('成功', 
                `账单获取完成！\n` +
                `获取日期: ${data.date || '昨天'}\n` +
                `获取记录: ${data.total_fetched || 0} 条\n` +
                `成功存储: ${data.success_stored || 0} 条\n` +
                `重复记录: ${data.duplicates || 0} 条`
            );
            
            // 刷新数据
            setTimeout(() => {
                refreshData();
            }, 1000);
        } else {
            showMessage('失败', response.data.message || '获取账单失败');
        }
    } catch (error) {
        hideLoadingModal();
        console.error('获取账单失败:', error);
        
        if (error.code === 'ECONNABORTED') {
            showMessage('超时', '获取账单超时，请稍后重试');
        } else {
            showMessage('错误', '获取账单失败: ' + error.message);
        }
    }
}

// 刷新数据
function refreshData() {
    loadStats();
    if (isSearching()) {
        searchBills(currentPage);
    } else {
        loadBills(currentPage);
    }
}

// 显示加载模态框
function showLoadingModal(text) {
    elements.loadingText.textContent = text;
    elements.loadingModal.style.display = 'block';
}

// 隐藏加载模态框
function hideLoadingModal() {
    elements.loadingModal.style.display = 'none';
}

// 显示消息模态框
function showMessage(title, message) {
    elements.messageTitle.textContent = title;
    elements.messageText.textContent = message;
    elements.messageModal.style.display = 'block';
}

// 关闭消息模态框
function closeMessageModal() {
    elements.messageModal.style.display = 'none';
}
