<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统状态 - 支付宝账单管理系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        .status-card.success {
            border-color: #52c41a;
            background: #f6ffed;
        }
        .status-card.warning {
            border-color: #faad14;
            background: #fffbe6;
        }
        .status-card.error {
            border-color: #ff4d4f;
            background: #fff2f0;
        }
        .status-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }
        .status-card.success .status-icon {
            color: #52c41a;
        }
        .status-card.warning .status-icon {
            color: #faad14;
        }
        .status-card.error .status-icon {
            color: #ff4d4f;
        }
        .status-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        .status-desc {
            color: #666;
            font-size: 14px;
        }
        .info-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .info-section h3 {
            margin-top: 0;
            color: #333;
        }
        .info-list {
            list-style: none;
            padding: 0;
        }
        .info-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e8e8e8;
        }
        .info-list li:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: 600;
            color: #333;
            display: inline-block;
            width: 120px;
        }
        .info-value {
            color: #666;
        }
        .actions {
            text-align: center;
            margin-top: 30px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            background: #1890ff;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #40a9ff;
        }
        .btn.secondary {
            background: #f0f0f0;
            color: #333;
        }
        .btn.secondary:hover {
            background: #d9d9d9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 系统启动成功！</h1>
            <p>支付宝账单管理系统已成功启动并运行</p>
        </div>
        
        <div class="content">
            <div class="status-grid">
                <?php
                // 检查各个组件状态
                $statuses = [];
                
                // 检查数据库连接
                try {
                    $config = include 'database.php';
                    $pdo = new PDO(
                        "mysql:host={$config['host']};dbname={$config['database']};charset={$config['charset']}",
                        $config['username'],
                        $config['password']
                    );
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM zfb");
                    $count = $stmt->fetch()['count'];
                    $statuses['database'] = [
                        'status' => 'success',
                        'title' => '数据库连接',
                        'desc' => "已连接，共 {$count} 条记录",
                        'icon' => '✅'
                    ];
                } catch (Exception $e) {
                    $statuses['database'] = [
                        'status' => 'error',
                        'title' => '数据库连接',
                        'desc' => '连接失败',
                        'icon' => '❌'
                    ];
                }
                
                // 检查Python环境
                $pythonCheck = shell_exec('python3 --version 2>&1');
                if (strpos($pythonCheck, 'Python') !== false) {
                    $statuses['python'] = [
                        'status' => 'success',
                        'title' => 'Python环境',
                        'desc' => trim($pythonCheck),
                        'icon' => '🐍'
                    ];
                } else {
                    $statuses['python'] = [
                        'status' => 'error',
                        'title' => 'Python环境',
                        'desc' => '未找到Python',
                        'icon' => '❌'
                    ];
                }
                
                // 检查PHP环境
                $statuses['php'] = [
                    'status' => 'success',
                    'title' => 'PHP环境',
                    'desc' => 'PHP ' . PHP_VERSION,
                    'icon' => '🔧'
                ];
                
                // 检查Web服务器
                $statuses['webserver'] = [
                    'status' => 'success',
                    'title' => 'Web服务器',
                    'desc' => '运行在 localhost:8000',
                    'icon' => '🌐'
                ];
                
                foreach ($statuses as $key => $status):
                ?>
                <div class="status-card <?= $status['status'] ?>">
                    <div class="status-icon"><?= $status['icon'] ?></div>
                    <div class="status-title"><?= $status['title'] ?></div>
                    <div class="status-desc"><?= $status['desc'] ?></div>
                </div>
                <?php endforeach; ?>
            </div>
            
            <div class="info-section">
                <h3>📋 系统信息</h3>
                <ul class="info-list">
                    <li>
                        <span class="info-label">项目名称:</span>
                        <span class="info-value">支付宝账单管理系统</span>
                    </li>
                    <li>
                        <span class="info-label">访问地址:</span>
                        <span class="info-value">http://localhost:8000</span>
                    </li>
                    <li>
                        <span class="info-label">启动时间:</span>
                        <span class="info-value"><?= date('Y-m-d H:i:s') ?></span>
                    </li>
                    <li>
                        <span class="info-label">运行模式:</span>
                        <span class="info-value">开发模式（测试环境）</span>
                    </li>
                    <li>
                        <span class="info-label">数据库:</span>
                        <span class="info-value">MySQL (zfb_data)</span>
                    </li>
                </ul>
            </div>
            
            <div class="info-section">
                <h3>🚀 快速开始</h3>
                <ul class="info-list">
                    <li>
                        <span class="info-label">1. 查看主界面:</span>
                        <span class="info-value">点击下方"进入主界面"按钮</span>
                    </li>
                    <li>
                        <span class="info-label">2. 测试功能:</span>
                        <span class="info-value">点击"获取最新账单"按钮测试系统</span>
                    </li>
                    <li>
                        <span class="info-label">3. 配置API:</span>
                        <span class="info-value">编辑 .env 文件配置支付宝API密钥</span>
                    </li>
                    <li>
                        <span class="info-label">4. 查看文档:</span>
                        <span class="info-value">阅读 README.md 了解详细配置</span>
                    </li>
                </ul>
            </div>
            
            <div class="actions">
                <a href="/" class="btn">🏠 进入主界面</a>
                <a href="/api.php?action=get_stats" class="btn secondary">📊 查看API状态</a>
            </div>
        </div>
    </div>
</body>
</html>
