#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库连接模块
用于连接MySQL数据库，读取和更新zfb表中的交易记录
"""

import os
import threading
import mysql.connector
from mysql.connector import Error
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class DatabaseConnector:
    """数据库连接类"""

    # 线程锁，用于保护数据库操作
    _lock = threading.Lock()

    def __init__(self):
        """初始化数据库连接"""
        try:
            # 从环境变量或配置文件读取数据库配置
            self.connection = mysql.connector.connect(
                host=os.getenv('DB_HOST', '127.0.0.1'),
                user=os.getenv('DB_USER', 'root'),
                password=os.getenv('DB_PASSWORD', 'chenasp@123'),
                database=os.getenv('DB_NAME', 'zfb_data'),
                charset='utf8mb4'
            )

            if self.connection.is_connected():
                self.cursor = self.connection.cursor(dictionary=True)
                print("数据库连接成功")
        except Error as e:
            print(f"数据库连接错误: {e}")
            raise

    def close(self):
        """关闭数据库连接"""
        if hasattr(self, 'connection') and self.connection.is_connected():
            self.cursor.close()
            self.connection.close()
            print("数据库连接已关闭")

    def get_all_expense_transactions(self, force_reclassify=True):
        """
        获取所有支出类交易记录

        Args:
            force_reclassify: 是否强制重新分类所有记录，包括已有分类的记录

        Returns:
            list: 交易记录列表
        """
        with self._lock:
            try:
                if force_reclassify:
                    # 获取所有支出类交易记录
                    query = """
                    SELECT z.*, c.name as category_name, a.name as account_name
                    FROM zfb z
                    LEFT JOIN categories c ON z.category_id = c.id
                    LEFT JOIN accounts a ON z.account_id = a.id
                    WHERE z.transaction_type = 'expense'
                    ORDER BY z.month DESC, z.gmt_create DESC
                    """
                    self.cursor.execute(query)
                else:
                    # 只获取未分类或分类为"其他支出"(ID=12)的交易记录
                    query = """
                    SELECT z.*, c.name as category_name, a.name as account_name
                    FROM zfb z
                    LEFT JOIN categories c ON z.category_id = c.id
                    LEFT JOIN accounts a ON z.account_id = a.id
                    WHERE z.transaction_type = 'expense'
                    AND (z.category_id IS NULL OR z.category_id = 12)
                    ORDER BY z.month DESC, z.gmt_create DESC
                    """
                    self.cursor.execute(query)

                return self.cursor.fetchall()
            except Error as e:
                print(f"查询交易记录错误: {e}")
                return []

    def get_expense_categories(self):
        """
        获取所有支出分类

        Returns:
            dict: 分类名称到ID的映射
        """
        with self._lock:
            try:
                query = "SELECT * FROM categories WHERE type = 'expense' ORDER BY name"
                self.cursor.execute(query)
                categories = self.cursor.fetchall()

                # 创建分类名称到ID的映射
                category_map = {}
                for category in categories:
                    category_map[category['name']] = category['id']

                return category_map
            except Error as e:
                print(f"查询分类错误: {e}")
                return {}

    def update_transaction_category(self, order_no, month, category_id):
        """
        更新交易记录的分类

        Args:
            order_no: 订单号
            month: 月份
            category_id: 分类ID

        Returns:
            bool: 更新是否成功
        """
        with self._lock:
            try:
                query = """
                UPDATE zfb
                SET category_id = %s
                WHERE alipay_order_no = %s AND month = %s
                """
                self.cursor.execute(query, (category_id, order_no, month))
                self.connection.commit()
                return True
            except Error as e:
                print(f"更新交易记录错误: {e}")
                self.connection.rollback()
                return False

    def get_ai_model_settings(self):
        """
        获取AI模型设置

        Returns:
            dict: AI模型设置
        """
        with self._lock:
            try:
                # 查询AI模型设置
                settings = {}

                # 查询API URL
                self.cursor.execute("SELECT value FROM settings WHERE `key` = 'ai_api_url'")
                result = self.cursor.fetchone()
                if result:
                    settings['api_url'] = result['value']

                # 查询API Key
                self.cursor.execute("SELECT value FROM settings WHERE `key` = 'ai_api_key'")
                result = self.cursor.fetchone()
                if result:
                    settings['api_key'] = result['value']

                # 查询模型名称
                self.cursor.execute("SELECT value FROM settings WHERE `key` = 'ai_model_name'")
                result = self.cursor.fetchone()
                if result:
                    settings['model_name'] = result['value']

                return settings
            except Error as e:
                print(f"查询AI模型设置错误: {e}")
                return {}

    def insert_bill_record(self, bill_data):
        """
        插入账单记录到数据库

        Args:
            bill_data: 账单数据字典，包含以下字段：
                - alipay_order_no: 支付宝订单号
                - month: 月份
                - gmt_create: 创建时间
                - goods_title: 商品标题
                - total_amount: 金额
                - trade_status: 交易状态
                - trade_type: 交易类型
                - transaction_type: 交易类型(收入/支出)
                - account_id: 账户ID (可选)
                - category_id: 分类ID (可选)

        Returns:
            bool: 插入是否成功
        """
        with self._lock:
            try:
                # 检查记录是否已存在
                check_query = """
                SELECT COUNT(*) as count FROM zfb
                WHERE alipay_order_no = %s AND month = %s
                """
                self.cursor.execute(check_query, (bill_data['alipay_order_no'], bill_data['month']))
                result = self.cursor.fetchone()

                if result['count'] > 0:
                    print(f"记录已存在: {bill_data['alipay_order_no']}")
                    return False

                # 插入新记录
                insert_query = """
                INSERT INTO zfb (
                    alipay_order_no, month, gmt_create, goods_title,
                    total_amount, trade_status, trade_type, transaction_type,
                    account_id, category_id
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """

                values = (
                    bill_data['alipay_order_no'],
                    bill_data['month'],
                    bill_data['gmt_create'],
                    bill_data['goods_title'],
                    bill_data['total_amount'],
                    bill_data['trade_status'],
                    bill_data['trade_type'],
                    bill_data['transaction_type'],
                    bill_data.get('account_id'),
                    bill_data.get('category_id')
                )

                self.cursor.execute(insert_query, values)
                self.connection.commit()
                print(f"成功插入记录: {bill_data['alipay_order_no']}")
                return True

            except Error as e:
                print(f"插入账单记录错误: {e}")
                self.connection.rollback()
                return False

    def get_all_transactions(self, limit=100, offset=0):
        """
        获取所有交易记录（分页）

        Args:
            limit: 每页记录数
            offset: 偏移量

        Returns:
            list: 交易记录列表
        """
        with self._lock:
            try:
                query = """
                SELECT z.*, c.name as category_name, a.name as account_name
                FROM zfb z
                LEFT JOIN categories c ON z.category_id = c.id
                LEFT JOIN accounts a ON z.account_id = a.id
                ORDER BY z.month DESC, z.gmt_create DESC
                LIMIT %s OFFSET %s
                """
                self.cursor.execute(query, (limit, offset))
                return self.cursor.fetchall()
            except Error as e:
                print(f"查询交易记录错误: {e}")
                return []

    def get_transaction_count(self):
        """
        获取交易记录总数

        Returns:
            int: 记录总数
        """
        with self._lock:
            try:
                query = "SELECT COUNT(*) as count FROM zfb"
                self.cursor.execute(query)
                result = self.cursor.fetchone()
                return result['count'] if result else 0
            except Error as e:
                print(f"查询记录总数错误: {e}")
                return 0
