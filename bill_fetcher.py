#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
账单获取主程序
整合支付宝API调用和数据库操作，获取账单并存储到数据库
"""

import sys
import json
import datetime
import argparse
from typing import List, Dict
from db_connector import DatabaseConnector
from alipay_client import AlipayBillClient


class BillFetcher:
    """账单获取器"""

    def __init__(self):
        """初始化"""
        self.db = None
        self.alipay_client = None
        
    def initialize(self):
        """初始化数据库连接和支付宝客户端"""
        try:
            # 初始化数据库连接
            self.db = DatabaseConnector()
            print("数据库连接初始化成功")
            
            # 初始化支付宝客户端
            self.alipay_client = AlipayBillClient()
            print("支付宝客户端初始化成功")
            
            return True
        except Exception as e:
            print(f"初始化失败: {e}")
            return False

    def fetch_and_store_bills(self, bill_date: str = None) -> Dict:
        """
        获取并存储账单数据

        Args:
            bill_date: 账单日期，格式：YYYY-MM-DD，默认为昨天

        Returns:
            Dict: 执行结果
        """
        if not self.db or not self.alipay_client:
            if not self.initialize():
                return {
                    'success': False,
                    'message': '初始化失败',
                    'data': {}
                }

        try:
            # 设置默认日期为昨天
            if not bill_date:
                yesterday = datetime.date.today() - datetime.timedelta(days=1)
                bill_date = yesterday.strftime('%Y-%m-%d')

            print(f"开始获取 {bill_date} 的账单数据...")

            # 从支付宝获取账单数据
            bills = self.alipay_client.fetch_bills_for_date(bill_date)
            
            if not bills:
                return {
                    'success': False,
                    'message': f'未获取到 {bill_date} 的账单数据',
                    'data': {'date': bill_date, 'count': 0}
                }

            print(f"获取到 {len(bills)} 条账单记录，开始存储到数据库...")

            # 存储到数据库
            success_count = 0
            error_count = 0
            duplicate_count = 0

            for bill in bills:
                try:
                    result = self.db.insert_bill_record(bill)
                    if result:
                        success_count += 1
                    else:
                        duplicate_count += 1
                except Exception as e:
                    print(f"存储账单记录失败: {e}, 记录: {bill}")
                    error_count += 1

            print(f"账单存储完成: 成功 {success_count} 条, 重复 {duplicate_count} 条, 错误 {error_count} 条")

            return {
                'success': True,
                'message': f'成功获取并存储 {bill_date} 的账单数据',
                'data': {
                    'date': bill_date,
                    'total_count': len(bills),
                    'success_count': success_count,
                    'duplicate_count': duplicate_count,
                    'error_count': error_count
                }
            }

        except Exception as e:
            error_msg = f"获取账单过程中发生错误: {e}"
            print(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'data': {}
            }

    def fetch_bills_for_month(self, month: str = None) -> Dict:
        """
        获取指定月份的所有账单数据

        Args:
            month: 月份，格式：YYYY-MM，默认为当前月份

        Returns:
            Dict: 执行结果
        """
        if not month:
            month = datetime.date.today().strftime('%Y-%m')

        try:
            # 获取该月份的所有日期
            year, month_num = map(int, month.split('-'))
            
            # 计算该月的天数
            if month_num == 12:
                next_month = datetime.date(year + 1, 1, 1)
            else:
                next_month = datetime.date(year, month_num + 1, 1)
            
            current_month = datetime.date(year, month_num, 1)
            days_in_month = (next_month - current_month).days

            print(f"开始获取 {month} 月的账单数据，共 {days_in_month} 天...")

            total_success = 0
            total_duplicate = 0
            total_error = 0
            processed_days = 0

            # 逐日获取账单
            for day in range(1, days_in_month + 1):
                bill_date = f"{year}-{month_num:02d}-{day:02d}"
                
                # 不获取未来日期的账单
                if datetime.datetime.strptime(bill_date, '%Y-%m-%d').date() > datetime.date.today():
                    continue

                print(f"正在处理 {bill_date}...")
                result = self.fetch_and_store_bills(bill_date)
                
                if result['success']:
                    data = result['data']
                    total_success += data.get('success_count', 0)
                    total_duplicate += data.get('duplicate_count', 0)
                    total_error += data.get('error_count', 0)
                    processed_days += 1
                else:
                    print(f"获取 {bill_date} 账单失败: {result['message']}")

            print(f"月度账单获取完成: 处理 {processed_days} 天, 成功 {total_success} 条, 重复 {total_duplicate} 条, 错误 {total_error} 条")

            return {
                'success': True,
                'message': f'成功获取 {month} 月的账单数据',
                'data': {
                    'month': month,
                    'processed_days': processed_days,
                    'total_success': total_success,
                    'total_duplicate': total_duplicate,
                    'total_error': total_error
                }
            }

        except Exception as e:
            error_msg = f"获取月度账单过程中发生错误: {e}"
            print(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'data': {}
            }

    def close(self):
        """关闭连接"""
        if self.db:
            self.db.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='支付宝账单获取工具')
    parser.add_argument('--date', type=str, help='指定日期 (YYYY-MM-DD)')
    parser.add_argument('--month', type=str, help='指定月份 (YYYY-MM)')
    parser.add_argument('--output', type=str, help='输出结果到JSON文件')
    
    args = parser.parse_args()
    
    fetcher = BillFetcher()
    
    try:
        if args.month:
            # 获取指定月份的账单
            result = fetcher.fetch_bills_for_month(args.month)
        elif args.date:
            # 获取指定日期的账单
            result = fetcher.fetch_and_store_bills(args.date)
        else:
            # 默认获取昨天的账单
            result = fetcher.fetch_and_store_bills()
        
        # 输出结果
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2, default=str)
            print(f"结果已保存到: {args.output}")
        else:
            print(json.dumps(result, ensure_ascii=False, indent=2, default=str))
        
        # 返回结果供PHP调用
        if result['success']:
            sys.exit(0)
        else:
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"程序执行失败: {e}")
        sys.exit(1)
    finally:
        fetcher.close()


if __name__ == '__main__':
    main()
