#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
账单获取主程序
整合支付宝API调用和数据库操作，获取账单并存储到数据库
"""

import sys
import json
import datetime
import argparse
from typing import List, Dict
from db_connector import DatabaseConnector
from alipay_client import AlipayBillClient


class BillFetcher:
    """账单获取器"""

    def __init__(self):
        """初始化"""
        self.db = None
        self.alipay_client = None
        
    def initialize(self):
        """初始化数据库连接和支付宝客户端"""
        try:
            # 初始化数据库连接
            self.db = DatabaseConnector()
            print("数据库连接初始化成功")
            
            # 初始化支付宝客户端
            self.alipay_client = AlipayBillClient()
            print("支付宝客户端初始化成功")
            
            return True
        except Exception as e:
            print(f"初始化失败: {e}")
            return False

    def fetch_and_store_bills(self, bill_date: str = None) -> Dict:
        """
        获取并存储账单数据

        Args:
            bill_date: 账单日期，格式：YYYY-MM-DD，默认为昨天

        Returns:
            Dict: 执行结果
        """
        if not self.db or not self.alipay_client:
            if not self.initialize():
                return {
                    'success': False,
                    'message': '初始化失败',
                    'data': {}
                }

        try:
            # 设置默认日期为昨天
            if not bill_date:
                yesterday = datetime.date.today() - datetime.timedelta(days=1)
                bill_date = yesterday.strftime('%Y-%m-%d')

            print(f"开始获取 {bill_date} 的账单数据...")

            # 从支付宝获取账单数据
            bills = self.alipay_client.fetch_bills_for_date(bill_date)
            
            if not bills:
                return {
                    'success': False,
                    'message': f'未获取到 {bill_date} 的账单数据',
                    'data': {'date': bill_date, 'count': 0}
                }

            print(f"获取到 {len(bills)} 条账单记录，开始存储到数据库...")

            # 存储到数据库
            success_count = 0
            error_count = 0
            duplicate_count = 0

            for bill in bills:
                try:
                    result = self.db.insert_bill_record(bill)
                    if result:
                        success_count += 1
                    else:
                        duplicate_count += 1
                except Exception as e:
                    print(f"存储账单记录失败: {e}, 记录: {bill}")
                    error_count += 1

            # 返回结果
            result = {
                'success': True,
                'message': f'账单获取完成',
                'data': {
                    'date': bill_date,
                    'total_fetched': len(bills),
                    'success_stored': success_count,
                    'duplicates': duplicate_count,
                    'errors': error_count
                }
            }

            print(f"账单获取完成: 获取{len(bills)}条，成功存储{success_count}条，重复{duplicate_count}条，错误{error_count}条")
            return result

        except Exception as e:
            error_msg = f"获取账单过程中发生错误: {e}"
            print(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'data': {}
            }

    def fetch_bills_for_month(self, year: int, month: int) -> Dict:
        """
        获取指定月份的所有账单

        Args:
            year: 年份
            month: 月份

        Returns:
            Dict: 执行结果
        """
        try:
            # 计算该月的所有日期
            import calendar
            
            # 获取该月的天数
            days_in_month = calendar.monthrange(year, month)[1]
            
            total_results = {
                'success': True,
                'message': f'{year}年{month}月账单获取完成',
                'data': {
                    'month': f'{year}-{month:02d}',
                    'total_days': days_in_month,
                    'processed_days': 0,
                    'total_fetched': 0,
                    'success_stored': 0,
                    'duplicates': 0,
                    'errors': 0
                }
            }

            # 逐日获取账单
            for day in range(1, days_in_month + 1):
                bill_date = f'{year}-{month:02d}-{day:02d}'
                
                # 不获取未来日期的账单
                if datetime.datetime.strptime(bill_date, '%Y-%m-%d').date() >= datetime.date.today():
                    continue
                
                print(f"正在获取 {bill_date} 的账单...")
                day_result = self.fetch_and_store_bills(bill_date)
                
                total_results['data']['processed_days'] += 1
                
                if day_result['success']:
                    day_data = day_result['data']
                    total_results['data']['total_fetched'] += day_data.get('total_fetched', 0)
                    total_results['data']['success_stored'] += day_data.get('success_stored', 0)
                    total_results['data']['duplicates'] += day_data.get('duplicates', 0)
                    total_results['data']['errors'] += day_data.get('errors', 0)
                else:
                    total_results['data']['errors'] += 1

            return total_results

        except Exception as e:
            error_msg = f"获取月度账单过程中发生错误: {e}"
            print(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'data': {}
            }

    def close(self):
        """关闭连接"""
        if self.db:
            self.db.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='支付宝账单获取工具')
    parser.add_argument('--date', type=str, help='指定日期 (YYYY-MM-DD)')
    parser.add_argument('--month', type=str, help='指定月份 (YYYY-MM)')
    parser.add_argument('--output', type=str, help='输出结果到JSON文件')
    
    args = parser.parse_args()
    
    fetcher = BillFetcher()
    
    try:
        if args.month:
            # 获取指定月份的账单
            try:
                year, month = map(int, args.month.split('-'))
                result = fetcher.fetch_bills_for_month(year, month)
            except ValueError:
                print("月份格式错误，请使用 YYYY-MM 格式")
                sys.exit(1)
        else:
            # 获取指定日期或昨天的账单
            result = fetcher.fetch_and_store_bills(args.date)
        
        # 输出结果
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2, default=str)
            print(f"结果已保存到: {args.output}")
        else:
            print("\n=== 执行结果 ===")
            print(json.dumps(result, ensure_ascii=False, indent=2, default=str))
        
        # 返回适当的退出码
        sys.exit(0 if result['success'] else 1)
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"程序执行异常: {e}")
        sys.exit(1)
    finally:
        fetcher.close()


if __name__ == '__main__':
    main()
