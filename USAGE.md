# 使用指南

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置数据库和支付宝API信息。

### 3. 测试系统

```bash
python3 test_system.py
```

### 4. 获取账单

```bash
# 获取昨天的账单
python3 bill_fetcher.py

# 获取指定日期的账单
python3 bill_fetcher.py --date 2024-01-01

# 获取指定月份的账单
python3 bill_fetcher.py --month 2024-01
```

## 配置说明

### 数据库配置

在 `.env` 文件中配置：

```env
DB_HOST=127.0.0.1
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=zfb_data
```

### 支付宝API配置

1. 在支付宝开放平台创建应用
2. 获取应用ID和私钥
3. 在 `.env` 文件中配置：

```env
ALIPAY_APP_ID=your_app_id
ALIPAY_PRIVATE_KEY_PATH=key.pem
ALIPAY_DEBUG=False
```

4. 将私钥保存为 `key.pem` 文件

## 文件说明

- `alipay_client.py` - 支付宝API客户端
- `bill_fetcher.py` - 账单获取主程序
- `db_connector.py` - 数据库连接器
- `test_system.py` - 系统测试脚本
- `requirements.txt` - Python依赖
- `.env.example` - 环境变量配置示例
- `key.pem` - 支付宝私钥文件
- `config/database.php` - 数据库配置文件（PHP格式）

## 常见问题

### 1. 数据库连接失败

检查数据库服务是否启动，确认连接信息是否正确。

### 2. 支付宝API调用失败

检查应用ID和私钥是否正确，确认应用已获得相应权限。

### 3. 依赖安装失败

使用虚拟环境：

```bash
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```
